//
//  FMChooseBrokerViewController.m
//  QCYZT
//
//  Created by zeng on 2022/10/25.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMChooseBrokerViewController.h"
#import "FMChooseBrokerCell.h"
#import "FMSwitchBrokerCell.h"
#import "HttpRequestTool+Stock.h"

@interface FMChooseBrokerViewController ()<UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) NSArray *dataArr;
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) UIView *bottomView;

@property (nonatomic, strong) FMChooseBrokerModel *choosedModel;

@end

@implementation FMChooseBrokerViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.view.backgroundColor = UIColor.up_contentBgColor;
        
    if (self.type == ChooseBrokerTypeChoose) {
        self.title = @"选择证券公司";
    } else {
        self.title = @"切换证券公司";
    }
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.equalTo(@0);
        make.bottom.equalTo(@(-UI_SAFEAREA_BOTTOM_HEIGHT));
    }];
    self.tableView.hidden = YES;
    
    [self requestData];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self configNavRedColor];
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (self.type == ChooseBrokerTypeChoose) {
        return self.dataArr.count + 1;
    }
    return self.dataArr.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (self.type == ChooseBrokerTypeChoose) {
        if (indexPath.row == self.dataArr.count) {
            UITableViewCell *cell = [UITableViewCell new];
            cell.selectionStyle = UITableViewCellSelectionStyleNone;
            cell.contentView.backgroundColor = UIColor.up_contentBgColor;
            return cell;
        }
        FMChooseBrokerCell *cell = [tableView reuseCellClass:[FMChooseBrokerCell class]];
        cell.model = self.dataArr[indexPath.row];
        return cell;
    }
    
    FMSwitchBrokerCell *cell = [tableView reuseCellClass:[FMSwitchBrokerCell class]];
    cell.model = self.dataArr[indexPath.row];
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (self.type == ChooseBrokerTypeChoose) {
        if (indexPath.row == self.dataArr.count) {
            CGFloat areaHeight = UI_SCREEN_HEIGHT - UI_SAFEAREA_TOP_HEIGHT - UI_SAFEAREA_BOTTOM_HEIGHT;
            CGFloat dataHeight = 98 * self.dataArr.count + 30 + 80; // 30是bottomView顶部预留高度
            if (areaHeight - 50 > dataHeight) { // 50是底部预留高度
                return areaHeight - 20 - dataHeight;
            }
            return 30;
        }
        
        return 98.0f;
    }
    return 76.0f;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:NO];
    
    if (self.type == ChooseBrokerTypeSwitch) {
        self.choosedModel.choosed = NO;
        FMChooseBrokerModel *model = self.dataArr[indexPath.row];
        model.choosed = YES;
        self.choosedModel = model;
        
        [FMUserDefault setArchiverData:model forKey:@"DefaultChoosedBroker"];
        [self.navigationController popViewControllerAnimated:YES];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            if (self.switchBlock) {
                self.switchBlock(model);
            }
        });
        
        [self.tableView reloadData];
    }
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    return [UIView new];
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

- (void)requestData {
    [HttpRequestTool requestBrokerListStart:^{
        [SVProgressHUD show];
    } failure:^{
        self.tableView.hidden = NO;
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
        [self.view showReloadNetworkViewWithBlock:^{
            [self requestData];
        }];
    } success:^(NSDictionary *dic) {
        self.tableView.hidden = NO;
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [SVProgressHUD dismiss];
            
            self.dataArr = [NSArray modelArrayWithClass:[FMChooseBrokerModel class] json:dic[@"data"]];
            if (!self.dataArr.count) {
                [self.tableView showNoDataViewWithImage:ImageWithName(@"chooseBroker_placeholder") string:@"功能正在维护中" attributes:nil offsetY:180];
                if (self.type == ChooseBrokerTypeChoose) {
                    self.tableView.tableFooterView = nil;
                }
            } else {
                [self.tableView dismissNoDataView];
                if (self.type == ChooseBrokerTypeChoose) {
                    self.tableView.tableFooterView = self.bottomView;
                }
            }
            
            if (self.type == ChooseBrokerTypeSwitch) {
                for (FMChooseBrokerModel *model in self.dataArr) {
                    if ([model.brokerId isEqualToString:self.choosedBrokerId]) {
                        model.choosed = YES;
                        break;
                    }
                }
            }
            
            [self.tableView reloadData];
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            [self.tableView showReloadNetworkViewWithBlock:^{
                [self requestData];
            }];
        }
    }];
}


- (void)phoneBtnClick {
    NSString *str = [NSString stringWithFormat:@"tel://%@", [FMUserDefault getSeting:AppInit_ContactUs_Phone]];
    [[UIApplication sharedApplication] openURL:[NSURL URLWithString:str] options:@{} completionHandler:nil];
}

- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain delegate:self dataSource:self viewController:self];
        [_tableView registerCellClass:[FMChooseBrokerCell class]];
        [_tableView registerCellClass:[FMSwitchBrokerCell class]];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.backgroundColor = UIColor.up_contentBgColor;
    }
    
    return _tableView;
}

- (UIView *)bottomView {
    if (!_bottomView) {
        _bottomView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, 80)];

        UIView *sepline = [_bottomView addSepLineWithBlock:^(MASConstraintMaker * _Nonnull make) {
            make.left.equalTo(@20);
            make.right.equalTo(@-20);
            make.top.equalTo(@20);
            make.height.equalTo(@0.7);
        }];
        sepline.backgroundColor = UIColor.fm_sepline_color;

        ZLTagLabel *tagLabel = [[ZLTagLabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:UIColor.up_textSecondary1Color backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        tagLabel.widthPadding = 20;
        [_bottomView addSubview:tagLabel];
        [tagLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.equalTo(sepline);
        }];
        tagLabel.text = @"证券服务由合作证券公司提供";

        NSString *phoneStr = [NSString stringWithFormat:@"客服电话：%@", [FMUserDefault getSeting:AppInit_ContactUs_Phone]];
        UIButton *phoneBtn = [[UIButton alloc] initWithFrame:CGRectZero font:FontWithSize(13) normalTextColor:ColorWithHex(0x0074fa) backgroundColor:FMClearColor title:phoneStr image:ImageWithName(@"stock_brokerChoosePhone") target:self action:@selector(phoneBtnClick)];
        [_bottomView addSubview:phoneBtn];
        [phoneBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(tagLabel.mas_bottom).offset(10);
            make.centerX.equalTo(@0);
        }];

        UILabel *timeLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:UIColor.up_textSecondary2Color backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        [_bottomView addSubview:timeLabel];
        [timeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(phoneBtn.mas_bottom).offset(2);
            make.centerX.equalTo(@0);
        }];
        timeLabel.text = @"（工作日 09:00-18:00）";
    }

    return _bottomView;
}

@end

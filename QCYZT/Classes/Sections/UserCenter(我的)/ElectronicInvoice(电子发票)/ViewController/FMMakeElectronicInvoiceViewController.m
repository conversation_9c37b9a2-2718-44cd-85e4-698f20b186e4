//
//  FMMakeElectronicInvoiceViewController.m
//  QCYZT
//
//  Created by zeng on 2022/9/2.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMMakeElectronicInvoiceViewController.h"
#import "FMMakeElectronicInvoiceCell.h"
#import "HttpRequestTool+Invoice.h"
#import "NSString+characterJudge.h"

@interface FMMakeElectronicInvoiceViewController ()<UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) UIView *tableHeaderView;
@property (nonatomic, strong) UIView *tableFooterView;
@property (nonatomic, strong) UIButton *commitBtn;

@property (nonatomic, strong) NSArray *dataArr;
@property (nonatomic, copy) NSString *mail;

@end

@implementation FMMakeElectronicInvoiceViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.title = @"开电子发票";
    self.mail = @"";
    
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.insets(UIEdgeInsetsMake(0, 0, 0, 0));
    }];
    
    [self requestData];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    [self configNavWhiteColorWithCloseSEL:@selector(backArrowClicked)];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    
    [self configNavRedColor];
}

- (void)backArrowClicked {
    [self.navigationController popViewControllerAnimated:YES];
}

#pragma mark - tableViewDelegate
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.dataArr.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    NSArray *arr = self.dataArr[section];
    return arr.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    FMMakeElectronicInvoiceCell *cell = [tableView reuseCellClass:[FMMakeElectronicInvoiceCell class]];
    NSArray *arr = self.dataArr[indexPath.section];
    NSDictionary *dic = arr[indexPath.row];
    cell.leftLabel.text = dic[@"title"];
    cell.rightTF.text = dic[@"content"];
    [cell.rightTF addTarget:self action:@selector(textFieldDidChange:) forControlEvents:UIControlEventEditingChanged];
    if (indexPath.section == 0) {
        cell.rightTF.enabled = NO;
    } else {
        cell.rightTF.enabled = YES;
    }
    
    if (arr.count == 1) {
        cell.cellLocation = CellLocationOnlyOne;
    } else {
        if (indexPath.row == 0) {
            cell.cellLocation = CellLocationTop;
        } else if (indexPath.row == arr.count - 1) {
            cell.cellLocation = CellLocationBottom;
        } else {
            cell.cellLocation = CellLocationMiddle;
        }
    }
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 55.0f;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    UIView *view = [UIView new];
    UILabel *label = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(17) textColor:ColorWithHex(0x333333) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    [view addSubview:label];
    [label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.centerY.equalTo(@0);
    }];
    if (section == 0) {
        label.text = @"发票详情";
    } else {
        label.text = @"收件人信息";
    }
    return view;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return 49.0f;
}

-(UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

#pragma mark - NSNotification
- (void)textFieldDidChange:(UITextField *)textField {
    self.mail = textField.text;
    
    [self judgeCommitStatus];
}

#pragma mark - Request
- (void)requestData {
    WEAKSELF
    [HttpRequestTool requestInvoiceInfoWithOrderNo:self.orderNo start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
        [__weakSelf.view showReloadNetworkViewWithBlock:^{
            [__weakSelf requestData];
        }];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [SVProgressHUD dismiss];
            
            NSArray *arr1 = @[@{@"title":@"发票类型", @"content":@"电子发票"},
                              @{@"title":@"抬头类型", @"content":@"个人"},
                              @{@"title":@"发票抬头", @"content":[NSString stringWithFormat:@"%@", dic[@"data"][@"userName"]]},
                              @{@"title":@"发票内容", @"content":@"业务咨询付费"},
                              @{@"title":@"发票金额", @"content":[NSString stringWithFormat:@"%@元", dic[@"data"][@"orderPrice"]]}];
            NSArray *arr2 = @[@{@"title":@"邮箱地址", @"content":self.mail}];
            self.dataArr = @[arr1, arr2];
            
            [self.tableView reloadData];
            
            
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            [__weakSelf.view showReloadNetworkViewWithBlock:^{
                [__weakSelf requestData];
            }];
        }
    }];
}

#pragma mark - Private
- (void)judgeCommitStatus {
    if (self.mail.length) {
        [self.commitBtn setBackgroundColor:FMNavColor];
        self.commitBtn.enabled = YES;
    } else {
        [self.commitBtn setBackgroundColor:ColorWithHex(0xF7CCCD)];
        self.commitBtn.enabled = NO;
    }
}

- (void)commit {
    if (self.mail.length && ![self.mail isMailAddress]) {
        [SVProgressHUD showInfoWithStatus:@"请输入正确的邮箱"];
        return;
    }
    
    [HttpRequestTool requestSaveInvoiceWithOrderNo:self.orderNo mailAddress:self.mail start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [PushMessageView showWithTitle:@"提交成功" message:@"将在2个工作日内开具，将以邮箱形式通知。" noticeImage:nil sureTitle:@"确定" cancelTitle:nil clickSure:^{
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [self.navigationController popViewControllerAnimated:YES];
                });
            } clickCancel:nil];

        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

#pragma mark - setter/getter
- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped delegate:self dataSource:self viewController:self];
        _tableView.backgroundColor = ColorWithHex(0xf7f7f7);
        [_tableView registerCellClass:[FMMakeElectronicInvoiceCell class]];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.tableHeaderView = self.tableHeaderView;
        _tableView.tableFooterView = self.tableFooterView;
    }
    return _tableView;
}

- (UIView *)tableHeaderView {
    if (!_tableHeaderView) {
        _tableHeaderView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, 55)];
        
        UIView *whiteView = [UIView new];
        whiteView.backgroundColor = FMWhiteColor;
        [_tableHeaderView addSubview:whiteView];
        [whiteView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(@10);
            make.centerX.equalTo(@0);
            make.width.equalTo(@(UI_SCREEN_WIDTH - 30));
            make.height.equalTo(@45);
        }];
        UILabel *label1 = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(15) textColor:ColorWithHex(0x333333) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
        [whiteView addSubview:label1];
        [label1 mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(@15);
            make.centerY.equalTo(@0);
        }];
        label1.text = @"以电子发票进行开具";
        
//        UIView *orangeView = [UIView new];
//        orangeView.backgroundColor = ColorWithHex(0xFFF1E6);
//        [_tableHeaderView addSubview:orangeView];
//        [orangeView mas_makeConstraints:^(MASConstraintMaker *make) {
//            make.left.right.equalTo(whiteView);
//            make.bottom.equalTo(@0);
//            make.top.equalTo(whiteView.mas_bottom);
//        }];
//        UIImageView *imgV = [[UIImageView alloc] init];
//        [orangeView addSubview:imgV];
//        [imgV mas_makeConstraints:^(MASConstraintMaker *make) {
//            make.left.equalTo(@15);
//            make.centerY.equalTo(@0);
//        }];
//        imgV.image = ImageWithName(@"InvoiceTrumpet");
//        UILabel *label2 = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:ColorWithHex(0xfc6c00) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
//        [orangeView addSubview:label2];
//        [label2 mas_makeConstraints:^(MASConstraintMaker *make) {
//            make.left.equalTo(imgV.mas_right).offset(10);
//            make.centerY.equalTo(@0);
//        }];
//        label2.text = @"默认按合同名称开具，若有对公发票需求请联系客服";
        
        UIView *lxkfBgView = [[UIView alloc] init];
        [_tableHeaderView addSubview:lxkfBgView];
        [lxkfBgView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(@0);
            make.top.equalTo(@5);
            make.size.equalTo(@(CGSizeMake(80, 30)));
        }];
        [lxkfBgView layerAndBezierPathWithRect:CGRectMake(0, 0, 80, 30) cornerRadii:CGSizeMake(15, 15) byRoundingCorners:UIRectCornerTopLeft|UIRectCornerBottomLeft];
        lxkfBgView.backgroundColor = ColorWithHex(0x607088);
        lxkfBgView.userInteractionEnabled = YES;
        [lxkfBgView addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithActionBlock:^(id  _Nonnull sender) {
            NSString *str = [NSString stringWithFormat:@"tel://%@", [FMUserDefault getSeting:AppInit_ContactUs_Phone]];
            [[UIApplication sharedApplication] openURL:[NSURL URLWithString:str] options:@{} completionHandler:nil];
        }]];
        UILabel *lxkfLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(14) textColor:FMWhiteColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentRight];
        [lxkfBgView addSubview:lxkfLabel];
        [lxkfLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(@0);
            make.right.equalTo(@-10);
        }];
        lxkfLabel.text = @"联系客服";
    }
    
    return _tableHeaderView;
}

- (UIView *)tableFooterView {
    if (!_tableFooterView) {
        _tableFooterView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, 100)];
        
        UIButton *btn = [[UIButton alloc] initWithFrame:CGRectZero font:FontWithSize(17) normalTextColor:FMWhiteColor backgroundColor:FMNavColor title:@"提交电子发票" image:nil target:self action:@selector(commit)];
        [_tableFooterView addSubview:btn];
        [btn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(@15);
            make.right.equalTo(@-15);
            make.top.equalTo(@40);
            make.height.equalTo(@45);
        }];
        UI_View_Radius(btn, 22.5);
        self.commitBtn = btn;
        [self.commitBtn setBackgroundColor:ColorWithHex(0xF7CCCD)];
        self.commitBtn.enabled = NO;
    }
    
    return _tableFooterView;
}


@end
